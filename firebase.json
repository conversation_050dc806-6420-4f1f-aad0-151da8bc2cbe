{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"]}], "storage": {"rules": "storage.rules"}, "flutter": {"platforms": {"android": {"default": {"projectId": "car-accessories-thy", "appId": "1:518343099919:android:b9f6d50143e94c3033c57a", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "car-accessories-thy", "appId": "1:518343099919:ios:620f74c2c35d159633c57a", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "car-accessories-thy", "configurations": {"android": "1:518343099919:android:b9f6d50143e94c3033c57a", "ios": "1:518343099919:ios:620f74c2c35d159633c57a"}}}}}}