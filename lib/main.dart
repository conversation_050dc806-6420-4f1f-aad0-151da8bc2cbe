import 'package:car_accessories/config/supabase_config.dart';
import 'package:car_accessories/firebase_options.dart';
import 'package:car_accessories/router/app_router.dart';
import 'package:car_accessories/services/storage_setup_helprt.dart';
import 'package:car_accessories/services/supabase_auth_brdge.dart';
import 'package:car_accessories/services/supabase_storage_service.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'services/error_handling_service.dart';
import 'services/backup_service.dart';
import 'services/monitoring_service.dart';
import 'dart:developer' as developer;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'providers/auth_provider.dart';
//Zerubabel H Nzowa
//6MekSApSajSvii3hI4tJ
//1ZOs82E3sXUvpvg2dUV6

//fVAFofUmoSHTxwwGa23u

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );

  // Initialize Firebase-Supabase auth bridge
  await SupabaseAuthBridge.initialize();

  // Initialize Supabase storage buckets
  await SupabaseStorageService.initializeBuckets();

  // Run storage diagnostics
  await StorageSetupHelper.runDiagnostics();

  // Initialize services
  await _initializeServices();

  runApp(ProviderScope(child: ErrorBoundary(child: const MyApp())));
}

Future<void> _initializeServices() async {
  try {
    // Initialize error handling service
    final errorHandler = ErrorHandlingService();
    errorHandler.setupGlobalErrorHandler();

    // Initialize monitoring service
    final monitoringService = MonitoringService();
    await monitoringService.initialize();

    // Log app initialization
    await monitoringService.logEvent('app_initialized', {
      'timestamp': DateTime.now().toIso8601String(),
      'version': '1.0.0',
    });

    // Schedule automatic backup (daily at 2 AM)
    final backupService = BackupService();
    await backupService.scheduleAutomaticBackup(
      backupType: BackupService.fullBackup,
      interval: const Duration(days: 1),
    );
  } catch (e) {
    // Log initialization error
    developer.log('Failed to initialize services: $e', name: 'Main', error: e);
  }
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> with WidgetsBindingObserver {
  final MonitoringService _monitoringService = MonitoringService();
  final ErrorHandlingService _errorHandler = ErrorHandlingService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Track app lifecycle
    _monitoringService.setAppActive(true);
    _initFCM();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _monitoringService.setAppActive(true);
        _monitoringService.logEvent('app_resumed', {
          'timestamp': DateTime.now().toIso8601String(),
        });
        break;
      case AppLifecycleState.paused:
        _monitoringService.setAppActive(false);
        _monitoringService.logEvent('app_paused', {
          'timestamp': DateTime.now().toIso8601String(),
        });
        break;
      case AppLifecycleState.detached:
        _monitoringService.setAppActive(false);
        _monitoringService.logEvent('app_detached', {
          'timestamp': DateTime.now().toIso8601String(),
        });
        break;
      default:
        break;
    }
  }

  Future<void> _initFCM() async {
    final messaging = FirebaseMessaging.instance;
    await messaging.requestPermission();
    final token = await messaging.getToken();
    if (token != null) {
      _updateFcmToken(token);
    }
    FirebaseMessaging.instance.onTokenRefresh.listen(_updateFcmToken);
  }

  void _updateFcmToken(String token) async {
    final user = ref.read(authProvider).user;
    if (user != null && user.fcmToken != token) {
      await FirebaseFirestore.instance.collection('users').doc(user.id).update({
        'fcmToken': token,
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final router = ref.watch(routerProvider);

    return MaterialApp.router(
      title: 'SHEIN Style',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFE91E63), // Shein pink
          brightness: Brightness.light,
        ).copyWith(
          primary: const Color(0xFFE91E63), // Shein pink
          secondary: const Color(0xFF000000), // Black
          surface: const Color(0xFFFFFBFE), // Off-white
          onSurface: const Color(0xFF1C1B1F), // Dark text
          outline: const Color(0xFFE0E0E0), // Light gray borders
        ),
        useMaterial3: true,
        fontFamily: 'Inter',
        textTheme: const TextTheme(
          displayLarge: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            letterSpacing: -0.5,
          ),
          headlineLarge: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w600,
            letterSpacing: -0.25,
          ),
          headlineMedium: TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
          titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
          titleMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
          bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
          labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFE91E63),
            foregroundColor: Colors.white,
            elevation: 0,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            foregroundColor: const Color(0xFFE91E63),
            side: const BorderSide(color: Color(0xFFE91E63)),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        cardTheme: const CardTheme(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(12)),
            side: BorderSide(color: Color(0xFFE0E0E0), width: 1),
          ),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Color(0xFF1C1B1F),
          elevation: 0,
          centerTitle: false,
          titleTextStyle: TextStyle(
            color: Color(0xFF1C1B1F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      debugShowCheckedModeBanner: false,
      routerConfig: router,
    );
  }
}
