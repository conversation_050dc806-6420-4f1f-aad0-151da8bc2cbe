name: car_accessories
description: "A new Flutter project."
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 1.0.0+1

environment:
  sdk: ^3.7.2
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  firebase_core: ^3.13.0
  cloud_firestore: ^5.6.6
  firebase_auth: ^5.5.2
  firebase_storage: any

  image_picker: ^1.1.2
  flutter_local_notifications: ^19.1.0
  flutter_rating_bar: ^4.0.1
  cached_network_image: ^3.4.1
  uuid: ^4.5.1
  http: ^1.3.0
  fl_chart: ^0.71.0
  flutter_slidable: ^4.0.0
  smooth_page_indicator: ^1.2.1
  flutter_riverpod: ^2.6.1
  intl: ^0.20.2
  go_router: ^15.1.2
  badges: ^3.1.2
  url_launcher: ^6.2.4

  dio: ^5.4.0
  path_provider: ^2.1.1
  crypto: ^3.0.3
  shared_preferences: ^2.5.3
  google_sign_in: ^6.3.0
  pdf: ^3.11.3
  share_plus: ^11.0.0
  shimmer: ^3.0.0
  lottie: ^3.1.2
  hugeicons: ^0.0.11
  flutter_staggered_grid_view: ^0.7.0
  firebase_analytics: ^11.5.0
  supabase_flutter: ^2.9.1
  firebase_messaging: ^15.2.8
  cloud_functions: ^5.6.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
